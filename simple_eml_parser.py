#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版EML文件解析器
"""

import os
import re
import email
import base64
import pandas as pd
from datetime import datetime
import glob

def decode_base64_gb2312(content):
    """解码GB2312编码的base64内容"""
    try:
        decoded_bytes = base64.b64decode(content)
        return decoded_bytes.decode('gb2312', errors='ignore')
    except Exception as e:
        print(f"解码错误: {e}")
        return ""

def parse_eml_file(file_path):
    """解析EML文件"""
    print(f"正在解析: {file_path}")
    
    try:
        # 直接读取文件内容
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 查找base64编码的内容
        base64_pattern = r'Content-Transfer-Encoding: base64\s*\n\s*\n(.*?)(?=\n------|\nContent-Type|\Z)'
        matches = re.findall(base64_pattern, content, re.DOTALL)
        
        all_text = ""
        for match in matches:
            # 清理base64内容
            clean_base64 = re.sub(r'\s', '', match)
            if len(clean_base64) > 100:  # 过滤掉太短的内容（可能是图片等）
                decoded_text = decode_base64_gb2312(clean_base64)
                all_text += decoded_text + "\n"
        
        print(f"解码后的内容长度: {len(all_text)}")
        if all_text:
            print(f"内容预览: {all_text[:200]}...")
        
        # 提取人员信息
        personnel_info = extract_personnel_info(all_text)
        return personnel_info
        
    except Exception as e:
        print(f"解析失败: {e}")
        return []

def extract_personnel_info(text):
    """提取人员信息"""
    personnel_list = []
    
    # 清理文本
    text = text.replace('\r', '').replace('\n', ' ')
    
    # 查找姓名模式
    name_patterns = [
        r'姓名[：:]\s*([^\s，,；;]{2,10})',
        r'联系人[：:]\s*([^\s，,；;]{2,10})',
        r'负责人[：:]\s*([^\s，,；;]{2,10})',
    ]
    
    # 查找单位模式
    unit_patterns = [
        r'单位[：:]\s*([^，,；;\n]{5,50})',
        r'公司[：:]\s*([^，,；;\n]{5,50})',
        r'([^，,；;\s]{5,30}(?:公司|单位|局|委|办|部|厅|院|中心|集团))',
    ]
    
    # 查找职务模式
    position_patterns = [
        r'职务[：:]\s*([^\s，,；;]{2,20})',
        r'职位[：:]\s*([^\s，,；;]{2,20})',
        r'([^\s，,；;]*(?:经理|主任|总监|总裁|董事|部长|科长|处长|局长|主席|书记|秘书))',
    ]
    
    # 查找电话模式
    phone_patterns = [
        r'电话[：:]\s*([0-9\-\s]{7,20})',
        r'手机[：:]\s*([0-9\-\s]{11,20})',
        r'联系电话[：:]\s*([0-9\-\s]{7,20})',
        r'(\d{3,4}[-\s]?\d{7,8})',  # 固定电话
        r'(1[3-9]\d{9})',  # 手机号
    ]
    
    # 提取所有匹配项
    names = []
    units = []
    positions = []
    phones = []
    
    for pattern in name_patterns:
        matches = re.findall(pattern, text)
        names.extend([m.strip() for m in matches if len(m.strip()) >= 2])
    
    for pattern in unit_patterns:
        matches = re.findall(pattern, text)
        units.extend([m.strip() for m in matches])
    
    for pattern in position_patterns:
        matches = re.findall(pattern, text)
        positions.extend([m.strip() for m in matches])
    
    for pattern in phone_patterns:
        matches = re.findall(pattern, text)
        phones.extend([m.strip() for m in matches])
    
    # 去重
    names = list(set(names))
    units = list(set(units))
    positions = list(set(positions))
    phones = list(set(phones))
    
    print(f"找到姓名: {names}")
    print(f"找到单位: {units}")
    print(f"找到职务: {positions}")
    print(f"找到电话: {phones}")
    
    # 创建记录
    max_len = max(len(names), len(units), len(positions), len(phones), 1)
    
    for i in range(max_len):
        person_info = {
            '姓名': names[i] if i < len(names) else '',
            '单位': units[i] if i < len(units) else '',
            '职务': positions[i] if i < len(positions) else '',
            '联系电话': phones[i] if i < len(phones) else '',
            '提取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        personnel_list.append(person_info)
    
    return personnel_list

def main():
    """主函数"""
    print("=== 简化版EML文件解析器 ===")
    
    # 查找EML文件
    eml_files = glob.glob("网络安全宣传周/*.eml")
    
    if not eml_files:
        print("未找到EML文件")
        return
    
    print(f"找到 {len(eml_files)} 个EML文件")
    
    all_personnel = []
    
    for eml_file in eml_files:
        personnel_info = parse_eml_file(eml_file)
        all_personnel.extend(personnel_info)
    
    if all_personnel:
        # 保存到Excel
        df = pd.DataFrame(all_personnel)
        output_file = f"人员信息_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df.to_excel(output_file, index=False)
        print(f"结果已保存到: {output_file}")
        print(f"共提取 {len(all_personnel)} 条记录")
        
        # 显示结果
        print("\n=== 提取结果 ===")
        for i, person in enumerate(all_personnel, 1):
            print(f"{i}. 姓名: {person['姓名']}, 单位: {person['单位']}, 职务: {person['职务']}, 电话: {person['联系电话']}")
    else:
        print("未提取到任何人员信息")

if __name__ == "__main__":
    main()
