#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EML文件解析器
解析多个EML文件，提取人员信息（姓名、单位、职务、联系电话）并汇总成表格
"""

import os
import re
import email
import base64
import pandas as pd
from email.header import decode_header
from email.utils import parseaddr
import chardet
from datetime import datetime
import glob

class EMLParser:
    def __init__(self):
        self.personnel_data = []
        
    def decode_mime_words(self, s):
        """解码MIME编码的字符串"""
        if not s:
            return ""
        
        try:
            decoded_parts = decode_header(s)
            decoded_string = ""
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_string += part.decode(encoding)
                    else:
                        # 尝试检测编码
                        detected = chardet.detect(part)
                        encoding = detected.get('encoding', 'utf-8')
                        decoded_string += part.decode(encoding, errors='ignore')
                else:
                    decoded_string += str(part)
            return decoded_string
        except Exception as e:
            print(f"解码错误: {e}")
            return str(s)
    
    def decode_base64_content(self, content, charset='gb2312'):
        """解码base64内容"""
        try:
            decoded_bytes = base64.b64decode(content)
            return decoded_bytes.decode(charset, errors='ignore')
        except Exception as e:
            print(f"Base64解码错误: {e}")
            return content
    
    def extract_text_from_email(self, msg):
        """从邮件中提取文本内容"""
        text_content = ""
        
        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition", ""))
                
                # 跳过附件
                if "attachment" in content_disposition:
                    continue
                
                if content_type == "text/plain":
                    charset = part.get_content_charset() or 'utf-8'
                    payload = part.get_payload(decode=True)
                    if payload:
                        try:
                            text_content += payload.decode(charset, errors='ignore')
                        except:
                            text_content += str(payload)
                
                elif content_type == "text/html":
                    charset = part.get_content_charset() or 'utf-8'
                    payload = part.get_payload(decode=True)
                    if payload:
                        try:
                            html_content = payload.decode(charset, errors='ignore')
                            # 简单的HTML标签清理
                            import re
                            clean_text = re.sub(r'<[^>]+>', '', html_content)
                            text_content += clean_text
                        except:
                            pass
        else:
            # 非multipart邮件
            content_type = msg.get_content_type()
            if content_type in ["text/plain", "text/html"]:
                charset = msg.get_content_charset() or 'utf-8'
                payload = msg.get_payload(decode=True)
                if payload:
                    try:
                        if content_type == "text/html":
                            html_content = payload.decode(charset, errors='ignore')
                            clean_text = re.sub(r'<[^>]+>', '', html_content)
                            text_content = clean_text
                        else:
                            text_content = payload.decode(charset, errors='ignore')
                    except:
                        text_content = str(payload)
        
        return text_content
    
    def extract_personnel_info(self, text_content, email_subject="", sender=""):
        """从文本内容中提取人员信息"""
        personnel_list = []
        
        # 常见的人员信息模式
        patterns = {
            'name_patterns': [
                r'姓\s*名[：:]\s*([^\s\n\r，,；;]{2,10})',
                r'联系人[：:]\s*([^\s\n\r，,；;]{2,10})',
                r'负责人[：:]\s*([^\s\n\r，,；;]{2,10})',
                r'([^\s\n\r]{2,4})\s*[（(].*?[）)]',  # 姓名(职务)格式
            ],
            'unit_patterns': [
                r'单\s*位[：:]\s*([^\n\r]{5,50})',
                r'公\s*司[：:]\s*([^\n\r]{5,50})',
                r'机\s*构[：:]\s*([^\n\r]{5,50})',
                r'([^\s\n\r]{5,30}(?:公司|单位|局|委|办|部|厅|院|中心|集团))',
            ],
            'position_patterns': [
                r'职\s*务[：:]\s*([^\s\n\r，,；;]{2,20})',
                r'职\s*位[：:]\s*([^\s\n\r，,；;]{2,20})',
                r'([^\s\n\r]*(?:经理|主任|总监|总裁|董事|部长|科长|处长|局长|主席|书记|秘书))',
            ],
            'phone_patterns': [
                r'电\s*话[：:]\s*([0-9\-\s]{7,20})',
                r'手\s*机[：:]\s*([0-9\-\s]{11,20})',
                r'联系电话[：:]\s*([0-9\-\s]{7,20})',
                r'(\d{3,4}[-\s]?\d{7,8})',  # 固定电话
                r'(1[3-9]\d{9})',  # 手机号
            ]
        }
        
        # 提取信息
        names = []
        units = []
        positions = []
        phones = []
        
        for pattern in patterns['name_patterns']:
            matches = re.findall(pattern, text_content)
            names.extend([match.strip() for match in matches if len(match.strip()) >= 2])
        
        for pattern in patterns['unit_patterns']:
            matches = re.findall(pattern, text_content)
            units.extend([match.strip() for match in matches])
        
        for pattern in patterns['position_patterns']:
            matches = re.findall(pattern, text_content)
            positions.extend([match.strip() for match in matches])
        
        for pattern in patterns['phone_patterns']:
            matches = re.findall(pattern, text_content)
            phones.extend([match.strip() for match in matches])
        
        # 去重
        names = list(set(names))
        units = list(set(units))
        positions = list(set(positions))
        phones = list(set(phones))
        
        # 如果找到信息，创建记录
        if names or units or phones:
            # 简单的匹配逻辑，可以根据实际情况调整
            max_len = max(len(names), len(units), len(positions), len(phones))
            
            for i in range(max_len):
                person_info = {
                    '姓名': names[i] if i < len(names) else '',
                    '单位': units[i] if i < len(units) else '',
                    '职务': positions[i] if i < len(positions) else '',
                    '联系电话': phones[i] if i < len(phones) else '',
                    '邮件主题': email_subject,
                    '发件人': sender,
                    '提取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                personnel_list.append(person_info)
        
        return personnel_list
    
    def parse_eml_file(self, file_path):
        """解析单个EML文件"""
        print(f"正在解析: {file_path}")
        
        try:
            with open(file_path, 'rb') as f:
                msg = email.message_from_bytes(f.read())
            
            # 获取邮件基本信息
            subject = self.decode_mime_words(msg.get('Subject', ''))
            sender = self.decode_mime_words(msg.get('From', ''))
            
            print(f"  主题: {subject}")
            print(f"  发件人: {sender}")
            
            # 提取文本内容
            text_content = self.extract_text_from_email(msg)
            
            # 如果文本内容为空，尝试直接解析base64内容
            if not text_content.strip():
                # 查找base64编码的文本部分
                for part in msg.walk():
                    if part.get_content_type() == "text/plain":
                        encoding = part.get('Content-Transfer-Encoding', '')
                        if encoding == 'base64':
                            payload = part.get_payload()
                            if payload:
                                text_content = self.decode_base64_content(payload, 'gb2312')
                                break
            
            # 提取人员信息
            personnel_info = self.extract_personnel_info(text_content, subject, sender)
            
            if personnel_info:
                self.personnel_data.extend(personnel_info)
                print(f"  提取到 {len(personnel_info)} 条人员信息")
            else:
                print("  未找到人员信息")
                # 保存原始内容用于调试
                debug_info = {
                    '姓名': '',
                    '单位': '',
                    '职务': '',
                    '联系电话': '',
                    '邮件主题': subject,
                    '发件人': sender,
                    '提取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    '原始内容预览': text_content[:200] + '...' if len(text_content) > 200 else text_content
                }
                self.personnel_data.append(debug_info)
            
        except Exception as e:
            print(f"  解析失败: {e}")
            error_info = {
                '姓名': '',
                '单位': '',
                '职务': '',
                '联系电话': '',
                '邮件主题': f'解析失败: {os.path.basename(file_path)}',
                '发件人': '',
                '提取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '错误信息': str(e)
            }
            self.personnel_data.append(error_info)
    
    def parse_directory(self, directory_path):
        """解析目录中的所有EML文件"""
        eml_files = glob.glob(os.path.join(directory_path, "*.eml"))
        
        if not eml_files:
            print(f"在目录 {directory_path} 中未找到EML文件")
            return
        
        print(f"找到 {len(eml_files)} 个EML文件")
        
        for eml_file in eml_files:
            self.parse_eml_file(eml_file)
    
    def save_to_excel(self, output_file):
        """保存结果到Excel文件"""
        if not self.personnel_data:
            print("没有数据可保存")
            return
        
        df = pd.DataFrame(self.personnel_data)
        
        # 创建Excel文件
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='人员信息汇总', index=False)
            
            # 创建统计表
            if '单位' in df.columns:
                unit_stats = df['单位'].value_counts()
                unit_stats.to_excel(writer, sheet_name='单位统计')
            
            if '职务' in df.columns:
                position_stats = df['职务'].value_counts()
                position_stats.to_excel(writer, sheet_name='职务统计')
        
        print(f"结果已保存到: {output_file}")
        print(f"共提取 {len(self.personnel_data)} 条记录")

def main():
    """主函数"""
    parser = EMLParser()
    
    # 解析网络安全宣传周目录中的EML文件
    directory_path = "网络安全宣传周"
    
    if not os.path.exists(directory_path):
        print(f"目录 {directory_path} 不存在")
        return
    
    print("=== EML文件人员信息提取工具 ===")
    parser.parse_directory(directory_path)
    
    # 保存结果
    output_file = f"人员信息汇总_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    parser.save_to_excel(output_file)

if __name__ == "__main__":
    main()
