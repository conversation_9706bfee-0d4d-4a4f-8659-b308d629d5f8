# EML文件人员信息提取工具使用说明

## 工具概述

这是一个专门用于从EML邮件文件中提取人员信息的Python工具。能够自动解析邮件内容，提取姓名、单位、职务、联系电话等信息，并生成Excel格式的汇总报告。

## 功能特点

- ✅ **批量处理**: 支持同时处理多个EML文件
- ✅ **智能过滤**: 自动过滤邮件签名部分，只提取正文内容
- ✅ **多种提取模式**: 支持结构化提取和分别提取两种模式
- ✅ **编码支持**: 自动处理GB2312编码的base64内容
- ✅ **Excel报告**: 生成包含多个工作表的详细Excel报告
- ✅ **数据质量分析**: 提供数据完整度统计和质量报告

## 安装依赖

```bash
pip install pandas openpyxl
```

## 使用方法

### 1. 基本使用

将EML文件放在`网络安全宣传周`目录下，然后运行：

```bash
python final_eml_parser.py
```

### 2. 指定目录

```bash
python final_eml_parser.py --dir "你的EML文件目录"
```

### 3. 指定输出文件名

```bash
python final_eml_parser.py --output "人员信息汇总.xlsx"
```

### 4. 完整参数示例

```bash
python final_eml_parser.py --dir "邮件文件夹" --output "提取结果.xlsx"
```

## 输出文件结构

生成的Excel文件包含以下工作表：

### 1. 人员信息汇总
包含所有提取到的人员信息：
- 姓名
- 单位
- 职务
- 联系电话
- 邮件主题
- 文件名
- 提取时间
- 提取方式

### 2. 单位统计
按单位统计人员数量

### 3. 职务统计
按职务统计人员数量

### 4. 数据质量报告
- 序号
- 姓名
- 完整度 (x/4)
- 完整度百分比
- 提取方式
- 文件名

## 支持的邮件格式

- EML格式的邮件文件
- GB2312编码的base64内容
- 包含结构化人员信息的邮件正文

## 提取模式说明

### 结构化提取
适用于格式规范的邮件，如：
```
姓名：张三
单位：某某公司
职务：经理
联系电话：13800138000
```

### 分别提取
当结构化提取失败时，会分别查找各个字段：
- 使用正则表达式分别匹配姓名、单位、职务、电话
- 按顺序组合匹配结果

## 过滤规则

工具会自动过滤以下签名内容：
- 公司地址信息
- 传真号码
- 公司邮箱
- 英文免责声明
- 分隔线
- 其他签名标识

## 示例输出

```
=== EML文件人员信息提取工具 ===
扫描目录: 网络安全宣传周
找到 1 个EML文件

正在解析: 【山东旭正-2025年国家网络安全宣传周济南市活动启动仪式】-现场领牌人员信息.eml
解码后的内容长度: 872
过滤后的正文内容: 各位领导： 2025年9月9日（星期二）上午9：30...
结构化提取失败，尝试分别提取...
分别提取结果:
  姓名: ['薛斌']
  单位: ['山东旭正信息科技有限公司']
  职务: ['技术经理']
  电话: ['18678306506']

=== 提取完成 ===
结果已保存到: 人员信息提取结果_20250908_101234.xlsx
共提取 1 条记录

=== 提取结果摘要 ===
1. 薛斌 | 山东旭正信息科技有限公司 | 技术经理 | 18678306506

=== 统计信息 ===
有效姓名: 1/1 (100.0%)
有效单位: 1/1 (100.0%)
有效职务: 1/1 (100.0%)
有效电话: 1/1 (100.0%)
```

## 常见问题

### Q: 为什么提取不到信息？
A: 可能的原因：
1. 邮件编码格式不支持
2. 邮件内容格式不规范
3. 信息被误判为签名部分

### Q: 如何处理多个人员信息？
A: 工具会自动识别邮件中的多个人员信息，每个人员会生成一条记录。

### Q: 支持哪些电话格式？
A: 支持以下格式：
- 固定电话：0531-82928077
- 手机号：13800138000
- 带空格的号码：186 7830 6506

### Q: 如何自定义过滤规则？
A: 可以修改`filter_email_content`函数中的`signature_patterns`列表。

## 技术说明

- **编程语言**: Python 3.6+
- **主要依赖**: pandas, openpyxl
- **编码支持**: UTF-8, GB2312
- **正则表达式**: 用于模式匹配和内容提取

## 更新日志

- **v1.0**: 基础功能实现
- **v2.0**: 添加签名过滤功能
- **v3.0**: 优化提取算法，添加数据质量报告

---

*工具开发时间: 2025年9月*
*适用场景: 网络安全宣传周等活动的人员信息整理*
