#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版EML文件解析器
专门用于解析网络安全宣传周相关的人员信息
"""

import os
import re
import base64
import pandas as pd
from datetime import datetime
import glob

def decode_base64_gb2312(content):
    """解码GB2312编码的base64内容"""
    try:
        decoded_bytes = base64.b64decode(content)
        return decoded_bytes.decode('gb2312', errors='ignore')
    except Exception as e:
        print(f"解码错误: {e}")
        return ""

def clean_text(text):
    """清理文本内容"""
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    # 移除特殊字符
    text = text.replace('\r', '').replace('\n', ' ')
    return text.strip()

def extract_personnel_info_structured(text):
    """结构化提取人员信息"""
    personnel_list = []
    
    # 清理文本
    text = clean_text(text)
    
    # 查找结构化的人员信息块
    # 模式：姓名：xxx 单位：xxx 职务：xxx 联系电话：xxx
    structured_pattern = r'姓名[：:]\s*([^\s：:]{2,10}).*?单位[：:]\s*([^：:]{5,50}).*?职务[：:]\s*([^：:]{2,20}).*?联系电话[：:]\s*([0-9\-\s]{7,20})'
    
    matches = re.findall(structured_pattern, text, re.DOTALL)
    
    for match in matches:
        name, unit, position, phone = match
        person_info = {
            '姓名': name.strip(),
            '单位': unit.strip(),
            '职务': position.strip(),
            '联系电话': phone.strip(),
            '提取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '数据来源': '结构化提取'
        }
        personnel_list.append(person_info)
    
    # 如果结构化提取失败，尝试分别提取
    if not personnel_list:
        print("结构化提取失败，尝试分别提取...")
        
        # 分别提取各个字段
        names = re.findall(r'姓名[：:]\s*([^\s：:，,；;]{2,10})', text)
        units = re.findall(r'单位[：:]\s*([^：:，,；;\n]{5,50})', text)
        positions = re.findall(r'职务[：:]\s*([^：:，,；;\n]{2,20})', text)
        phones = re.findall(r'联系电话[：:]\s*([0-9\-\s]{7,20})', text)
        
        # 也尝试提取其他格式的电话
        if not phones:
            phones = re.findall(r'(\d{3,4}[-\s]?\d{7,8})', text)  # 固定电话
            phones.extend(re.findall(r'(1[3-9]\d{9})', text))  # 手机号
        
        print(f"分别提取结果:")
        print(f"  姓名: {names}")
        print(f"  单位: {units}")
        print(f"  职务: {positions}")
        print(f"  电话: {phones}")
        
        # 创建记录（假设按顺序对应）
        max_len = max(len(names), len(units), len(positions), len(phones))
        
        for i in range(max_len):
            person_info = {
                '姓名': names[i] if i < len(names) else '',
                '单位': units[i] if i < len(units) else '',
                '职务': positions[i] if i < len(positions) else '',
                '联系电话': phones[i] if i < len(phones) else '',
                '提取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '数据来源': '分别提取'
            }
            personnel_list.append(person_info)
    
    return personnel_list

def parse_eml_file(file_path):
    """解析EML文件"""
    print(f"\n正在解析: {os.path.basename(file_path)}")
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 提取邮件主题
        subject_match = re.search(r'Subject: (.+)', content)
        subject = subject_match.group(1) if subject_match else "未知主题"
        
        # 查找base64编码的内容
        base64_pattern = r'Content-Transfer-Encoding: base64\s*\n\s*\n(.*?)(?=\n------|\nContent-Type|\Z)'
        matches = re.findall(base64_pattern, content, re.DOTALL)
        
        all_text = ""
        for match in matches:
            # 清理base64内容
            clean_base64 = re.sub(r'\s', '', match)
            if len(clean_base64) > 100:  # 过滤掉太短的内容
                decoded_text = decode_base64_gb2312(clean_base64)
                all_text += decoded_text + "\n"
        
        print(f"解码后的内容长度: {len(all_text)}")
        if all_text:
            print(f"内容预览:\n{all_text[:300]}...")
        
        # 提取人员信息
        personnel_info = extract_personnel_info_structured(all_text)
        
        # 为每条记录添加邮件信息
        for person in personnel_info:
            person['邮件主题'] = subject
            person['文件名'] = os.path.basename(file_path)
        
        return personnel_info
        
    except Exception as e:
        print(f"解析失败: {e}")
        return []

def create_comprehensive_excel(personnel_data, output_file):
    """创建综合的Excel报告"""
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 主数据表
        df = pd.DataFrame(personnel_data)
        df.to_excel(writer, sheet_name='人员信息汇总', index=False)
        
        # 按单位统计
        if '单位' in df.columns and not df['单位'].empty:
            unit_stats = df['单位'].value_counts()
            unit_stats.to_excel(writer, sheet_name='单位统计')
        
        # 按职务统计
        if '职务' in df.columns and not df['职务'].empty:
            position_stats = df['职务'].value_counts()
            position_stats.to_excel(writer, sheet_name='职务统计')
        
        # 数据质量报告
        quality_report = []
        for i, person in enumerate(personnel_data, 1):
            completeness = sum([1 for field in ['姓名', '单位', '职务', '联系电话'] 
                              if person.get(field, '').strip()])
            quality_report.append({
                '序号': i,
                '姓名': person.get('姓名', ''),
                '完整度': f"{completeness}/4",
                '完整度百分比': f"{completeness/4*100:.1f}%",
                '数据来源': person.get('数据来源', ''),
                '文件名': person.get('文件名', '')
            })
        
        quality_df = pd.DataFrame(quality_report)
        quality_df.to_excel(writer, sheet_name='数据质量报告', index=False)

def main():
    """主函数"""
    print("=== 改进版EML文件人员信息提取工具 ===")
    
    # 查找EML文件
    eml_files = glob.glob("网络安全宣传周/*.eml")
    
    if not eml_files:
        print("未找到EML文件")
        return
    
    print(f"找到 {len(eml_files)} 个EML文件")
    
    all_personnel = []
    
    for eml_file in eml_files:
        personnel_info = parse_eml_file(eml_file)
        all_personnel.extend(personnel_info)
    
    if all_personnel:
        # 保存到Excel
        output_file = f"网络安全宣传周人员信息_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        create_comprehensive_excel(all_personnel, output_file)
        
        print(f"\n=== 提取完成 ===")
        print(f"结果已保存到: {output_file}")
        print(f"共提取 {len(all_personnel)} 条记录")
        
        # 显示结果摘要
        print(f"\n=== 提取结果摘要 ===")
        for i, person in enumerate(all_personnel, 1):
            print(f"{i}. {person.get('姓名', '未知')} | {person.get('单位', '未知')} | {person.get('职务', '未知')} | {person.get('联系电话', '未知')}")
        
        # 统计信息
        print(f"\n=== 统计信息 ===")
        valid_names = sum(1 for p in all_personnel if p.get('姓名', '').strip())
        valid_units = sum(1 for p in all_personnel if p.get('单位', '').strip())
        valid_positions = sum(1 for p in all_personnel if p.get('职务', '').strip())
        valid_phones = sum(1 for p in all_personnel if p.get('联系电话', '').strip())
        
        print(f"有效姓名: {valid_names}/{len(all_personnel)}")
        print(f"有效单位: {valid_units}/{len(all_personnel)}")
        print(f"有效职务: {valid_positions}/{len(all_personnel)}")
        print(f"有效电话: {valid_phones}/{len(all_personnel)}")
        
    else:
        print("未提取到任何人员信息")

if __name__ == "__main__":
    main()
