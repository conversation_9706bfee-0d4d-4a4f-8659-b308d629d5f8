<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <style>
        :root {
            --klein-blue: #002FA7;
            --bright-klein-blue: #0047D1;
            --light-klein-blue: #1F5FFF;
            --primary-blue: #1a73e8;
            --dark-blue: #0d47a1;
            --light-blue: #e8f0fe;
            --hover-blue: #f1f8ff;
            --gray-text: #5f6368;
            --border-color: #dadce0;
            --bg-color: #f8f9fa;
            --text-dark: #202124;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
            color: var(--text-dark);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        /* 系统标题栏样式 */
        .system-header {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            background: linear-gradient(135deg, var(--klein-blue) 0%, var(--bright-klein-blue) 100%);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            height: 60px;
            min-width: 280px;
        }
        
        .system-logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .system-logo:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .system-logo i {
            font-size: 20px;
            color: white;
        }
        
        .system-title {
            color: white;
            font-weight: 600;
            letter-spacing: 1px;
        }
        
        .system-title-main {
            font-size: 20px;
            line-height: 1.2;
            margin-bottom: 2px;
        }
        
        .system-title-sub {
            font-size: 12px;
            opacity: 0.8;
            font-weight: 400;
            letter-spacing: 0.5px;
        }
        
        /* 顶部菜单栏样式 - 使用克莱因蓝色系 */
        .top-menu {
            display: flex;
            background: linear-gradient(135deg, var(--klein-blue) 0%, var(--bright-klein-blue) 100%);
            box-shadow: 0 4px 20px rgba(0,47,167,0.3);
            z-index: 100;
            height: 60px;
        }
        
        .top-menu-container {
            display: flex;
            flex-grow: 1;
            padding: 0 20px;
        }
        
        .top-menu-item {
            padding: 15px 20px;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            user-select: none;
            color: rgba(255, 255, 255, 0.8);
            border-bottom: 3px solid transparent;
            margin-top: 0;
            height: 60px;
            box-sizing: border-box;
            border-radius: 8px 8px 0 0;
        }
        
        .top-menu-item i {
            margin-right: 8px;
            width: 18px;
            text-align: center;
        }
        
        .top-menu-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            transform: translateY(-2px);
        }
        
        .top-menu-item.active {
            color: #fff;
            background: rgba(255, 255, 255, 0.15);
            font-weight: 600;
            border-bottom: 3px solid #fff;
        }
        
        /* 主内容区域布局 */
        .main-container {
            display: flex;
            flex-grow: 1;
        }
        
        /* 侧边栏样式 - 克莱因蓝色系 */
        .side-menu {
            width: 260px;
            background: linear-gradient(180deg, #f8faff 0%, #f0f4ff 100%);
            padding: 15px 10px;
            box-shadow: 2px 0 20px rgba(0,47,167,0.1);
            overflow-y: auto;
            height: calc(100vh - 60px);
            flex-shrink: 0;
            border-right: 1px solid rgba(0,47,167,0.1);
        }
        
        .menu-item {
            padding: 12px 15px;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            user-select: none;
            color: var(--gray-text);
            border-radius: 8px;
            margin: 4px 0;
            border-left: 4px solid transparent;
            background: linear-gradient(90deg, rgba(0,47,167,0.08) 0%, rgba(0,47,167,0.03) 100%);
        }
        
        .menu-item i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }
        
        .menu-item:hover {
            background: linear-gradient(90deg, rgba(0,71,209,0.15) 0%, rgba(0,71,209,0.08) 100%);
            color: var(--bright-klein-blue);
            transform: translateX(8px);
        }
        
        .menu-item.active {
            color: var(--klein-blue);
            background: linear-gradient(90deg, rgba(0,47,167,0.2) 0%, rgba(0,47,167,0.1) 100%);
            font-weight: 600;
            border-left: 4px solid var(--klein-blue);
        }
        
        /* 二级菜单 */
        .sub-menu {
            padding-left: 20px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .menu-item.expanded + .sub-menu {
            max-height: 500px;
        }
        
        /* 三级菜单 */
        .third-menu {
            padding-left: 20px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .sub-menu-item.expanded + .third-menu {
            max-height: 500px;
        }
        
        .sub-menu-item, .third-menu-item {
            padding: 10px 15px;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            font-size: 13px;
            display: flex;
            align-items: center;
            user-select: none;
            margin: 2px 0;
            border-radius: 6px;
            color: var(--gray-text);
            border-left: 3px solid transparent;
            background: linear-gradient(90deg, rgba(0,71,209,0.06) 0%, rgba(0,71,209,0.02) 100%);
        }
        
        .sub-menu-item:hover, .third-menu-item:hover {
            background: linear-gradient(90deg, rgba(31,95,255,0.12) 0%, rgba(31,95,255,0.06) 100%);
            color: var(--light-klein-blue);
            transform: translateX(6px);
        }
        
        .sub-menu-item.active, .third-menu-item.active {
            color: var(--bright-klein-blue);
            background: linear-gradient(90deg, rgba(0,71,209,0.2) 0%, rgba(0,71,209,0.1) 100%);
            font-weight: 600;
            border-left: 3px solid var(--bright-klein-blue);
        }
        
        .third-menu-item {
            background: linear-gradient(90deg, rgba(31,95,255,0.04) 0%, rgba(31,95,255,0.01) 100%);
        }
        
        .third-menu-item:hover {
            transform: translateX(4px);
        }
        
        .has-children::after {
            content: "›";
            position: absolute;
            right: 15px;
            transition: transform 0.3s;
            font-size: 16px;
        }
        
        .has-children.expanded::after {
            transform: rotate(90deg);
        }
        
        /* 内容区域样式 */
        .content-area {
            flex-grow: 1;
            padding: 20px 20px 20px 10px;
            background: white;
            margin: 0;
            border-radius: 0;
            box-shadow: none;
            overflow-x: auto;
            overflow-y: auto;
            min-width: 0;
            max-width: none;
            height: calc(100vh - 60px);
        }
        
        /* 为内容区域内的组件添加卡片样式 */
        .content-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .content-section:last-child {
            margin-bottom: 0;
        }
        

        /* Tab页样式 */
        .tab-container {
            margin-bottom: 20px;
        }
        
        .tab-header {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            background: linear-gradient(135deg, #fff 0%, #f8faff 100%);
            border-radius: 8px 8px 0 0;
            padding: 0 10px;
        }
        
        .tab-item {
            padding: 15px 25px;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            font-size: 14px;
            color: var(--gray-text);
            border-bottom: 3px solid transparent;
            border-radius: 8px 8px 0 0;
            background: transparent;
        }
        
        .tab-item:hover {
            background: rgba(0,47,167,0.05);
            color: var(--bright-klein-blue);
        }
        
        .tab-item.active {
            color: var(--klein-blue);
            background: white;
            font-weight: 600;
            border-bottom: 3px solid var(--klein-blue);
        }
        
        .tab-content {
            display: none;
            padding: 20px;
            background: white;
            border-radius: 0 0 8px 8px;
            min-height: 600px;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* ICP备案库样式 */
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid var(--klein-blue);
        }
        
        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 6px 20px rgba(0,47,167,0.15);
        }
        
        .stats-card-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .stats-card-value {
            font-size: 24px;
            font-weight: bold;
            color: var(--klein-blue);
        }
        
        .status-normal {
            color: #52c41a;
        }
        
        /* 网络安全资产库样式 */
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 6px 20px rgba(0,47,167,0.15);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            border-radius: 8px 8px 0 0;
        }
        
        .stat-card.report::before { background-color: var(--klein-blue); }
        .stat-card.detect::before { background-color: #52c41a; }
        .stat-card.notify::before { background-color: #fa8c16; }
        .stat-card.total::before { background-color: #722ed1; }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .stat-title {
            font-size: 14px;
            color: #666;
        }
        
        .stat-icon {
            font-size: 24px;
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-desc {
            font-size: 12px;
            color: #999;
        }
        
        /* 表格样式优化 */
        .table-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
            width: 100%;
        }
        
        .table-responsive {
            overflow-x: auto;
            width: 100%;
            max-width: 100%;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        
        th {
            background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
            color: var(--klein-blue);
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
            cursor: pointer;
        }
        
        th:hover {
            background: linear-gradient(135deg, #f0f4ff 0%, #e8f0fe 100%);
        }
        
        tr:hover {
            background-color: rgba(0,47,167,0.03);
        }
        
        /* 搜索栏样式 */
        .search-bar {
            display: flex;
            margin-bottom: 20px;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border-color: var(--klein-blue);
            outline: none;
            box-shadow: 0 0 0 2px rgba(0,47,167,0.2);
        }
        
        .search-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, var(--klein-blue) 0%, var(--bright-klein-blue) 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,47,167,0.3);
        }
        
        /* 状态标签 */
        .source-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            color: white;
        }
        
        .source-tag.report {
            background-color: var(--klein-blue);
        }
        
        .source-tag.detect {
            background-color: #52c41a;
        }
        
        .source-tag.notify {
            background-color: #fa8c16;
        }
        
        .status-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-tag.pending {
            background-color: #fff2e8;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        
        .status-tag.imported {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-tag.failed {
            background-color: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        
        /* 筛选条件区域样式 */
        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .filter-title {
            font-size: 16px;
            font-weight: bold;
            color: var(--klein-blue);
            margin-bottom: 15px;
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .filter-group {
            display: grid;
            grid-template-columns: 100px 1fr;
            gap: 12px;
            align-items: center;
            min-width: 0;
        }
        
        .filter-label {
            font-size: 14px;
            color: #333;
            white-space: nowrap;
            text-align: right;
            font-weight: 500;
        }
        
        .filter-input, .filter-select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            font-size: 14px;
            width: 100%;
            min-width: 0;
            transition: all 0.3s ease;
        }
        
        .filter-input:focus, .filter-select:focus {
            border-color: var(--klein-blue);
            outline: none;
            box-shadow: 0 0 0 2px rgba(0,47,167,0.2);
        }
        
        /* 日期范围特殊处理 */
        .filter-group.date-range {
            grid-template-columns: 100px 1fr;
        }
        
        .filter-group.date-range .filter-input {
            min-width: 140px;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--klein-blue) 0%, var(--bright-klein-blue) 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,47,167,0.3);
        }
        
        .btn-success {
            background-color: #52c41a;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #45b518;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background-color: #fa8c16;
            color: white;
        }
        
        .btn-warning:hover {
            background-color: #e67e14;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #d9d9d9;
        }
        
        .btn-secondary:hover {
            background-color: #e6f4ff;
            border-color: var(--klein-blue);
            transform: translateY(-2px);
        }
        
        /* 操作按钮 */
        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
            transition: all 0.2s ease;
        }
        
        .action-btn.import {
            background-color: #52c41a;
            color: white;
        }
        
        .action-btn.import:hover {
            background-color: #45b518;
        }
        
        .action-btn.detail {
            background-color: var(--klein-blue);
            color: white;
        }
        
        .action-btn.detail:hover {
            background-color: var(--bright-klein-blue);
        }
        
        .action-btn.delete {
            background-color: #ff4d4f;
            color: white;
        }
        
        .action-btn.delete:hover {
            background-color: #d9363e;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .filter-row {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }
        
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }
            
            .side-menu {
                width: 100%;
                height: auto;
            }
            
            .top-menu {
                height: auto;
                flex-wrap: wrap;
            }
            
            .top-menu-item {
                height: auto;
            }
            
            .content-area {
                margin: 0;
                padding: 15px;
                height: auto;
            }
            
            .stats-overview {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .filter-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .filter-group {
                grid-template-columns: 1fr;
                gap: 8px;
            }
            
            .filter-label {
                text-align: left;
            }
            
            .action-buttons {
                justify-content: center;
            }
            
            .search-bar {
                flex-direction: column;
            }
            
            .search-input {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部菜单栏 -->
    <div class="top-menu">
        <!-- 系统标题栏 -->
        <div class="system-header">
            <div class="system-logo">
                <i class="fas fa-database"></i>
            </div>
            <div class="system-title">
                <div class="system-title-main">数据管理系统</div>
                <div class="system-title-sub"></div>
            </div>
        </div>
        
        <!-- 菜单项容器 -->
        <div class="top-menu-container">
            <div class="top-menu-item" onclick="setTopActive(this)">
                <i class="fas fa-chart-pie"></i>数据总览大屏
            </div>
            <div class="top-menu-item active" onclick="setTopActive(this)">
                <i class="fas fa-server"></i>资产数据
            </div>
            <div class="top-menu-item" onclick="setTopActive(this)">
                <i class="fas fa-shield-alt"></i>威胁数据
            </div>
            <div class="top-menu-item" onclick="setTopActive(this)">
                <i class="fas fa-info-circle"></i>情报数据
            </div>
            <div class="top-menu-item" onclick="setTopActive(this)">
                <i class="fas fa-industry"></i>产业数据
            </div>
            <div class="top-menu-item" onclick="setTopActive(this)">
                <i class="fas fa-users"></i>人才数据
            </div>
        </div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-container">
        <!-- 侧边栏 -->
        <div class="side-menu">
            <!-- 资产数据二级菜单 -->
            <div class="menu-item has-children" onclick="toggleMenu(this)">
                <i class="fas fa-database"></i>资产库
            </div>
            <div class="sub-menu">
                <div class="sub-menu-item" onclick="setActive(this)">单位档案</div>
                <div class="sub-menu-item" onclick="setActive(this)">IP档案</div>
                <div class="sub-menu-item" onclick="setActive(this)">系统档案</div>
                <div class="sub-menu-item" onclick="setActive(this)">小程序档案</div>
                <div class="sub-menu-item" onclick="setActive(this)">APP档案</div>
                <div class="sub-menu-item" onclick="setActive(this)">数据资产档案</div>
            </div>
            
            <div class="menu-item has-children" onclick="toggleMenu(this)">
                <i class="fas fa-tools"></i>资产治理
            </div>
            <div class="sub-menu">
                <div class="sub-menu-item" onclick="setActive(this)">资产核查</div>
                <div class="sub-menu-item" onclick="setActive(this)">单位数据治理</div>
                <div class="sub-menu-item" onclick="setActive(this)">系统数据治理</div>
                <div class="sub-menu-item" onclick="setActive(this)">IP数据治理</div>
            </div>
            
            <div class="menu-item has-children expanded" onclick="toggleMenu(this)">
                <i class="fas fa-archive"></i>原始库
            </div>
            <div class="sub-menu" style="max-height: 500px;">
                <div class="sub-menu-item" onclick="setActive(this)">原始资产库</div>
                <div class="sub-menu-item active" onclick="setActive(this)">ICP备案库</div>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content-area">
            <!-- ICP备案库内容 -->
            <div class="content-section icp-content" id="icp-content">
                <h2 style="color: var(--klein-blue); margin-bottom: 20px;">ICP备案库管理</h2>
                    
                    <div class="stats-cards">
                        <div class="stats-card">
                            <div class="stats-card-title">网站总数</div>
                            <div class="stats-card-value">10 条</div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-card-title">本月新增</div>
                            <div class="stats-card-value">2 条</div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-card-title">今日更新时间</div>
                            <div class="stats-card-value" style="font-size: 14px;">2023-04-19 09:15:37</div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-card-title">更新状态</div>
                            <div class="stats-card-value status-normal" style="font-size: 16px;">更新成功</div>
                        </div>
                    </div>
                    
                    <div class="search-bar">
                        <input type="text" class="search-input" placeholder="搜索网站名称、网站域名、备案许可证、主办者等...">
                        <button class="search-btn">搜索</button>
                    </div>
                    
                    <div class="table-container">
                        <div class="table-responsive">
                            <table>
                                <thead>
                                    <tr>
                                        <th>网站名称</th>
                                        <th>网站域名</th>
                                        <th>备案许可证</th>
                                        <th>主办者</th>
                                        <th>营业地</th>
                                        <th>接入地</th>
                                        <th>办公地址</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>济南市人民政府门户网站</td>
                                        <td>www.jinan.gov.cn</td>
                                        <td>鲁ICP备05039506号-1</td>
                                        <td>济南市人民政府办公厅</td>
                                        <td>山东省</td>
                                        <td>济南市</td>
                                        <td>济南市历下区龙鼎大道1号龙奥大厦</td>
                                    </tr>
                                    <tr>
                                        <td>济南日报</td>
                                        <td>www.jnrb.com.cn</td>
                                        <td>鲁ICP备09082651号-1</td>
                                        <td>济南日报社</td>
                                        <td>山东省</td>
                                        <td>济南市</td>
                                        <td>济南市市中区纬二路51号</td>
                                    </tr>
                                    <tr>
                                        <td>济南市教育局</td>
                                        <td>jyj.jinan.gov.cn</td>
                                        <td>鲁ICP备05039506号-2</td>
                                        <td>济南市教育局</td>
                                        <td>山东省</td>
                                        <td>济南市</td>
                                        <td>济南市市中区经七路73号</td>
                                    </tr>
                                    <tr>
                                        <td>济南市公安局</td>
                                        <td>jnga.jinan.gov.cn</td>
                                        <td>鲁ICP备05039506号-3</td>
                                        <td>济南市公安局</td>
                                        <td>山东省</td>
                                        <td>济南市</td>
                                        <td>济南市市中区经七路73号</td>
                                    </tr>
                                    <tr>
                                        <td>济南市卫生健康委员会</td>
                                        <td>jnwsjkw.jinan.gov.cn</td>
                                        <td>鲁ICP备05039506号-4</td>
                                        <td>济南市卫生健康委员会</td>
                                        <td>山东省</td>
                                        <td>济南市</td>
                                        <td>济南市市中区经七路89号</td>
                                    </tr>
                                    <tr>
                                        <td>济南市文化和旅游局</td>
                                        <td>whlyjj.jinan.gov.cn</td>
                                        <td>鲁ICP备05039506号-5</td>
                                        <td>济南市文化和旅游局</td>
                                        <td>山东省</td>
                                        <td>济南市</td>
                                        <td>济南市历下区龙鼎大道1号龙奥大厦</td>
                                    </tr>
                                    <tr>
                                        <td>济南市市场监督管理局</td>
                                        <td>scjgj.jinan.gov.cn</td>
                                        <td>鲁ICP备05039506号-6</td>
                                        <td>济南市市场监督管理局</td>
                                        <td>山东省</td>
                                        <td>济南市</td>
                                        <td>济南市历下区龙鼎大道1号龙奥大厦</td>
                                    </tr>
                                    <tr>
                                        <td>济南市生态环境局</td>
                                        <td>sthjj.jinan.gov.cn</td>
                                        <td>鲁ICP备05039506号-7</td>
                                        <td>济南市生态环境局</td>
                                        <td>山东省</td>
                                        <td>济南市</td>
                                        <td>济南市历下区龙鼎大道1号龙奥大厦</td>
                                    </tr>
                                    <tr>
                                        <td>济南市住房和城乡建设局</td>
                                        <td>jnzfcxjsj.jinan.gov.cn</td>
                                        <td>鲁ICP备05039506号-8</td>
                                        <td>济南市住房和城乡建设局</td>
                                        <td>山东省</td>
                                        <td>济南市</td>
                                        <td>济南市历下区龙鼎大道1号龙奥大厦</td>
                                    </tr>
                                    <tr>
                                        <td>济南市交通运输局</td>
                                        <td>jtysj.jinan.gov.cn</td>
                                        <td>鲁ICP备05039506号-9</td>
                                        <td>济南市交通运输局</td>
                                        <td>山东省</td>
                                        <td>济南市</td>
                                        <td>济南市历下区龙鼎大道1号龙奥大厦</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 原始资产库内容（网络安全资产库） -->
            <div class="content-section asset-content" id="asset-content" style="display: none;">
                <h2 style="color: var(--klein-blue); margin-bottom: 20px;">原始资产库管理</h2>
                    
                    <!-- 数据来源统计 -->
                    <div class="stats-overview">
                        <div class="stat-card report">
                            <div class="stat-header">
                                <span class="stat-title">单位上报</span>
                                <span class="stat-icon">📤</span>
                            </div>
                            <div class="stat-value">1,247</div>
                            <div class="stat-desc">本月新增 +89</div>
                        </div>
                        
                        <div class="stat-card detect">
                            <div class="stat-header">
                                <span class="stat-title">自主探测</span>
                                <span class="stat-icon">🔍</span>
                            </div>
                            <div class="stat-value">2,856</div>
                            <div class="stat-desc">本月新增 +156</div>
                        </div>
                        
                        <div class="stat-card notify">
                            <div class="stat-header">
                                <span class="stat-title">上级通报</span>
                                <span class="stat-icon">📢</span>
                            </div>
                            <div class="stat-value">456</div>
                            <div class="stat-desc">本月新增 +23</div>
                        </div>
                        
                        <div class="stat-card total">
                            <div class="stat-header">
                                <span class="stat-title">总资产数</span>
                                <span class="stat-icon">📊</span>
                            </div>
                            <div class="stat-value">4,559</div>
                            <div class="stat-desc">待导入 234</div>
                        </div>
                    </div>
                    
                    <!-- 筛选条件 -->
                    <div class="filter-section">
                        <div class="filter-title">筛选条件</div>
                        <div class="filter-row">
                            <div class="filter-group">
                                <label class="filter-label">数据来源：</label>
                                <select class="filter-select" id="sourceFilter">
                                    <option value="">全部来源</option>
                                    <option value="report">单位上报</option>
                                    <option value="detect">自主探测</option>
                                    <option value="notify">上级通报</option>
                                </select>
                            </div>
                            
                            <div class="filter-group">
                                <label class="filter-label">涉事单位：</label>
                                <input type="text" class="filter-input" id="unitFilter" placeholder="请输入单位名称">
                            </div>
                            
                            <div class="filter-group">
                                <label class="filter-label">IP地址：</label>
                                <input type="text" class="filter-input" id="ipFilter" placeholder="请输入IP地址">
                            </div>
                            
                            <div class="filter-group">
                                <label class="filter-label">域名：</label>
                                <input type="text" class="filter-input" id="domainFilter" placeholder="请输入域名">
                            </div>
                        </div>
                        
                        <div class="filter-row">
                            <div class="filter-group">
                                <label class="filter-label">资产名称：</label>
                                <input type="text" class="filter-input" id="assetNameFilter" placeholder="请输入资产名称">
                            </div>
                            
                            <div class="filter-group">
                                <label class="filter-label">端口：</label>
                                <input type="text" class="filter-input" id="portFilter" placeholder="请输入端口号">
                            </div>
                            
                            <div class="filter-group">
                                <label class="filter-label">导入状态：</label>
                                <select class="filter-select" id="statusFilter">
                                    <option value="">全部状态</option>
                                    <option value="pending">待导入</option>
                                    <option value="imported">已导入</option>
                                    <option value="failed">导入失败</option>
                                </select>
                            </div>
                            
                            <div class="filter-group date-range">
                                <label class="filter-label">发现时间：</label>
                                <div style="display: flex; gap: 8px; align-items: center;">
                                    <input type="date" class="filter-input" id="dateStart" style="flex: 1;">
                                    <span style="white-space: nowrap;">至</span>
                                    <input type="date" class="filter-input" id="dateEnd" style="flex: 1;">
                                </div>
                            </div>
                        </div>
                        
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="searchAssets()">查询</button>
                            <button class="btn btn-secondary" onclick="resetFilters()">重置</button>
                            <button class="btn btn-success" onclick="batchImport()">批量导入</button>
                            <button class="btn btn-warning" onclick="exportData()">导出数据</button>
                        </div>
                    </div>
                    
                    <div class="search-bar">
                        <input type="text" class="search-input" placeholder="搜索IP地址、域名、资产名称、涉事单位等...">
                        <button class="search-btn">搜索</button>
                    </div>
                    
                    <div class="table-container">
                        <div class="table-responsive">
                            <table>
                                <thead>
                                    <tr>
                                        <th style="width: 40px;">选择</th>
                                        <th>发现时间</th>
                                        <th>IP地址</th>
                                        <th>域名</th>
                                        <th>端口</th>
                                        <th>资产名称</th>
                                        <th>涉事单位</th>
                                        <th>数据来源</th>
                                        <th>导入状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><input type="checkbox" class="asset-checkbox"></td>
                                        <td>2024-08-27 10:30:15</td>
                                        <td>************</td>
                                        <td>web.jinan.gov.cn</td>
                                        <td>80,443</td>
                                        <td>Web服务器</td>
                                        <td>市政府办公厅</td>
                                        <td><span class="source-tag report">单位上报</span></td>
                                        <td><span class="status-tag pending">待导入</span></td>
                                        <td>
                                            <button class="action-btn import">导入治理</button>
                                            <button class="action-btn detail">查看详情</button>
                                            <button class="action-btn delete">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input type="checkbox" class="asset-checkbox"></td>
                                        <td>2024-08-27 09:15:42</td>
                                        <td>10.0.0.25</td>
                                        <td>未知</td>
                                        <td>22</td>
                                        <td>SSH服务</td>
                                        <td><未确认></td>
                                        <td><span class="source-tag detect">自主探测</span></td>
                                        <td><span class="status-tag imported">已导入</span></td>
                                        <td>
                                            <button class="action-btn detail">查看详情</button>
                                            <button class="action-btn delete">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input type="checkbox" class="asset-checkbox"></td>
                                        <td>2024-08-27 08:45:23</td>
                                        <td>172.16.0.100</td>
                                        <td>edu-admin.jinan.edu.cn</td>
                                        <td>3389</td>
                                        <td>远程桌面服务</td>
                                        <td>市教育局</td>
                                        <td><span class="source-tag notify">上级通报</span></td>
                                        <td><span class="status-tag pending">待导入</span></td>
                                        <td>
                                            <button class="action-btn import">导入治理</button>
                                            <button class="action-btn detail">查看详情</button>
                                            <button class="action-btn delete">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input type="checkbox" class="asset-checkbox"></td>
                                        <td>2024-08-26 16:20:10</td>
                                        <td>192.168.2.50</td>
                                        <td>未知</td>
                                        <td>1433</td>
                                        <td>SQL Server数据库</td>
                                        <td>市卫健委</td>
                                        <td><span class="source-tag report">单位上报</span></td>
                                        <td><span class="status-tag imported">已导入</span></td>
                                        <td>
                                            <button class="action-btn detail">查看详情</button>
                                            <button class="action-btn delete">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input type="checkbox" class="asset-checkbox"></td>
                                        <td>2024-08-26 14:55:30</td>
                                        <td>10.10.1.88</td>
                                        <td>未知</td>
                                        <td>21</td>
                                        <td>FTP服务器</td>
                                        <td><未确认></td>
                                        <td><span class="source-tag detect">自主探测</span></td>
                                        <td><span class="status-tag failed">导入失败</span></td>
                                        <td>
                                            <button class="action-btn import">导入治理</button>
                                            <button class="action-btn detail">查看详情</button>
                                            <button class="action-btn delete">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input type="checkbox" class="asset-checkbox"></td>
                                        <td>2024-08-25 15:20:45</td>
                                        <td>192.168.3.25</td>
                                        <td>finance.jinan.gov.cn</td>
                                        <td>80</td>
                                        <td>Nginx服务器</td>
                                        <td>市财政局</td>
                                        <td><span class="source-tag report">单位上报</span></td>
                                        <td><span class="status-tag pending">待导入</span></td>
                                        <td>
                                            <button class="action-btn import">导入治理</button>
                                            <button class="action-btn detail">查看详情</button>
                                            <button class="action-btn delete">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input type="checkbox" class="asset-checkbox"></td>
                                        <td>2024-08-25 12:10:30</td>
                                        <td>10.20.0.150</td>
                                        <td>未知</td>
                                        <td>443</td>
                                        <td>HTTPS服务</td>
                                        <td><未确认></td>
                                        <td><span class="source-tag detect">自主探测</span></td>
                                        <td><span class="status-tag imported">已导入</span></td>
                                        <td>
                                            <button class="action-btn detail">查看详情</button>
                                            <button class="action-btn delete">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input type="checkbox" class="asset-checkbox"></td>
                                        <td>2024-08-24 16:45:22</td>
                                        <td>172.20.5.100</td>
                                        <td>tourism.jinan.gov.cn</td>
                                        <td>8080</td>
                                        <td>Tomcat应用服务器</td>
                                        <td>市文旅局</td>
                                        <td><span class="source-tag notify">上级通报</span></td>
                                        <td><span class="status-tag pending">待导入</span></td>
                                        <td>
                                            <button class="action-btn import">导入治理</button>
                                            <button class="action-btn detail">查看详情</button>
                                            <button class="action-btn delete">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 当前激活的菜单项
        let activeMenuItem = null;
        
        // 设置顶部菜单项激活状态
        function setTopActive(element) {
            const siblings = element.parentElement.children;
            for (let sibling of siblings) {
                sibling.classList.remove('active');
            }
            element.classList.add('active');
        }
        
        // 设置侧边菜单项激活状态
        function setActive(element) {
            if (activeMenuItem && activeMenuItem !== element) {
                activeMenuItem.classList.remove('active');
            }
            element.classList.add('active');
            activeMenuItem = element;
        }
        
        // 切换子菜单显示/隐藏和内容显示
        function toggleMenu(element) {
            element.classList.toggle('expanded');
            setActive(element);
        }
        
        // 菜单项点击事件，用于切换内容区域
        function setActive(element) {
            if (activeMenuItem && activeMenuItem !== element) {
                activeMenuItem.classList.remove('active');
            }
            element.classList.add('active');
            activeMenuItem = element;
            
            // 根据菜单项切换内容显示
            const menuText = element.textContent.trim();
            if (menuText === 'ICP备案库') {
                showContent('icp-content');
            } else if (menuText === '原始资产库') {
                showContent('asset-content');
            }
        }
        
        // 显示指定内容区域
        function showContent(contentId) {
            // 隐藏所有内容区域
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });
            
            // 显示指定内容区域
            const targetContent = document.getElementById(contentId);
            if (targetContent) {
                targetContent.style.display = 'block';
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            activeMenuItem = document.querySelector('.menu-item.active');
        });
        
        // 资产库相关功能函数
        function searchAssets() {
            alert('执行资产搜索功能...');
        }
        
        function resetFilters() {
            // 重置所有筛选条件
            document.getElementById('sourceFilter').value = '';
            document.getElementById('unitFilter').value = '';
            document.getElementById('ipFilter').value = '';
            document.getElementById('domainFilter').value = '';
            document.getElementById('assetNameFilter').value = '';
            document.getElementById('portFilter').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('dateStart').value = '';
            document.getElementById('dateEnd').value = '';
            alert('筛选条件已重置');
        }
        
        function batchImport() {
            const checkboxes = document.querySelectorAll('.asset-checkbox:checked');
            if (checkboxes.length === 0) {
                alert('请先选择要导入的资产！');
                return;
            }
            if (confirm(`确定要批量导入${checkboxes.length}个资产到治理系统吗？`)) {
                alert(`成功导入${checkboxes.length}个资产到治理系统！`);
            }
        }
        
        function exportData() {
            alert('数据导出功能开发中...');
        }
        
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.asset-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }
    </script>
</body>
</html>